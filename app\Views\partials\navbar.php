<?php
/**
 * Navbar partial
 */
?>
<nav class="navbar default-layout-navbar col-lg-12 col-12 p-0 fixed-top d-flex flex-row">
    <div class="text-center navbar-brand-wrapper d-flex align-items-center justify-content-center">
        <a class="navbar-brand brand-logo" href="<?= baseUrl(); ?>"> <img src="<?= baseUrl('assets/images/fav-32x32.png'); ?>" alt="logo" />Ticketing App</a>
        <a class="navbar-brand brand-logo-mini" href="<?= baseUrl(); ?>"><img src="<?= baseUrl('assets/images/fav-32x32.png'); ?>" alt="logo" /></a>
    </div>
    <div class="navbar-menu-wrapper d-flex align-items-stretch">
        <button class="navbar-toggler navbar-toggler align-self-center" type="button" data-toggle="minimize">
            <span class="mdi mdi-menu"></span>
        </button>
        <div class="search-field d-none d-xl-block">
            <form class="d-flex align-items-center h-100" action="#">
                <div class="input-group">
                    <div class="input-group-prepend bg-transparent">
                        <i class="input-group-text border-0 mdi mdi-magnify"></i>
                    </div>
                    <input type="text" class="form-control bg-transparent border-0" placeholder="Search tickets">
                </div>
            </form>
        </div>
        <ul class="navbar-nav navbar-nav-right">
            <!-- Notification Bell Dropdown -->
            <li class="nav-item notification-dropdown dropdown">
                <button class="btn btn-link dropdown-toggle" type="button" id="notificationDropdown" 
                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="mdi mdi-bell-outline"></i>
                    <span class="notification-badge">0</span>
                </button>
                <div class="dropdown-menu dropdown-menu-right notification-dropdown-menu" 
                     aria-labelledby="notificationDropdown">
                    <div class="dropdown-header d-flex justify-content-between align-items-center">
                        <span>Notifications</span>
                        <button class="btn btn-sm btn-link" onclick="markAllNotificationsAsRead()">
                            Mark all as read
                        </button>
                    </div>
                    <div class="dropdown-divider"></div>
                    <!-- Notifications will be loaded here -->
                    <div class="text-center p-3">
                        <i class="mdi mdi-bell-outline h3 text-muted"></i>
                        <p class="text-muted">No notifications</p>
                    </div>
                </div>
            </li>
            <!-- End Notification Bell Dropdown -->

            <!-- Existing profile dropdown and other nav items -->
            <li class="nav-item nav-profile dropdown">
                <a class="nav-link dropdown-toggle" id="profileDropdown" href="#" data-toggle="dropdown" aria-expanded="false">
                    <div class="nav-profile-img">
                        <img src="<?= baseUrl('assets/images/agent.png'); ?>" alt="image">
                    </div>
                    <div class="nav-profile-text">
                        <p class="mb-1 text-black"><?= sanitize(getCurrentUser()['name']); ?></p>
                    </div>
                </a>
                <div class="dropdown-menu navbar-dropdown dropdown-menu-right p-0 border-0 font-size-sm" aria-labelledby="profileDropdown" data-x-placement="bottom-end">
                    <div class="p-3 text-center bg-primary">
                        <img class="img-avatar img-avatar48 img-avatar-thumb" src="<?= baseUrl('assets/images/agent.png'); ?>" alt="image">
                    </div>
                    <div class="p-2">
                        <h5 class="dropdown-header text-uppercase pl-2 text-dark">User Options</h5>
                        <a class="dropdown-item py-1 d-flex align-items-center justify-content-between" href="<?= baseUrl('profile'); ?>">
                            <span>Profile</span>
                            <span class="p-0">
                                <i class="mdi mdi-account-outline ml-1"></i>
                            </span>
                        </a>
                        <div role="separator" class="dropdown-divider"></div>
                        <h5 class="dropdown-header text-uppercase pl-2 text-dark mt-2">Actions</h5>
                        <a class="dropdown-item py-1 d-flex align-items-center justify-content-between" href="<?= baseUrl('logout'); ?>">
                            <span>Log Out</span>
                            <i class="mdi mdi-logout ml-1"></i>
                        </a>
                    </div>
                </div>
            </li>
        </ul>
        <button class="navbar-toggler navbar-toggler-right d-lg-none align-self-center" type="button" data-toggle="offcanvas">
            <span class="mdi mdi-menu"></span>
        </button>
    </div>
</nav>

<script>
// Load notifications when dropdown is opened
document.addEventListener('DOMContentLoaded', function() {
    const notificationDropdown = document.getElementById('notificationDropdown');
    if (notificationDropdown) {
        notificationDropdown.addEventListener('click', function() {
            loadNotifications();
        });
    }
});

function loadNotifications() {
    fetch('/notifications', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayNotifications(data.notifications);
        }
    })
    .catch(error => console.error('Failed to load notifications:', error));
}

function displayNotifications(notifications) {
    const menu = document.querySelector('.notification-dropdown-menu');
    const content = menu.querySelector('.dropdown-divider').nextElementSibling;

    if (notifications.length === 0) {
        content.innerHTML = `
            <div class="text-center p-3">
                <i class="mdi mdi-bell-outline h3 text-muted"></i>
                <p class="text-muted">No notifications</p>
            </div>
        `;
        return;
    }

    let html = '';
    notifications.forEach(notification => {
        const data = JSON.parse(notification.data || '{}');
        html += `
            <div class="notification-item ${notification.is_read ? '' : 'unread'}"
                 onclick="markNotificationAsRead(${notification.id})">
                <div class="notification-content">
                    <h6>${notification.title}</h6>
                    <p>${notification.message}</p>
                    <small class="text-muted">${formatDate(notification.created_at)}</small>
                </div>
            </div>
        `;
    });

    content.innerHTML = html;
}

function markNotificationAsRead(notificationId) {
    fetch(`/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            csrf_token: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the notification item
            const item = document.querySelector(`[onclick="markNotificationAsRead(${notificationId})"]`);
            if (item) {
                item.classList.remove('unread');
            }
            // Update badge
            if (window.notificationClient) {
                window.notificationClient.updateNotificationBadge();
            }
        }
    })
    .catch(error => console.error('Failed to mark notification as read:', error));
}

function markAllNotificationsAsRead() {
    fetch('/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            csrf_token: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove unread class from all items
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
            });
            // Update badge
            if (window.notificationClient) {
                window.notificationClient.updateNotificationBadge();
            }
        }
    })
    .catch(error => console.error('Failed to mark all notifications as read:', error));
}

function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) { // Less than 1 minute
        return 'Just now';
    } else if (diff < 3600000) { // Less than 1 hour
        return Math.floor(diff / 60000) + ' minutes ago';
    } else if (diff < 86400000) { // Less than 1 day
        return Math.floor(diff / 3600000) + ' hours ago';
    } else {
        return date.toLocaleDateString();
    }
}
</script>
